# Password Risk Test Patterns - Maintenance Summary

## Overview
Maintained `commands/test_password_risk.js` to include comprehensive test data for **all current password risk patterns** in the system.

## Current Implementation Status

### Total Test Coverage
- **Password Risk Patterns**: 13 patterns
- **Configuration Types**: 8 configurations  
- **Total Test Cases Generated**: **104 test cases**

### Password Risk Patterns (13 patterns)

#### Original Patterns (8 patterns)
1. `NO_LEAK` - No password leaks found
2. `LEAK` - Single password leak 
3. `LEAK_2` - Multiple password leaks (extensive list)
4. `LEAK_MULTIPLE_VALID` - Multiple leaks with valid (non-expired) data
5. `LEAK_HIGH_SEVERITY_EXPIRED` - High severity leak but expired data
6. `LEAK_HIGH_SEVERITY_VALID` - High severity leak with valid data (IsSpamList=true)
7. `LEAK_SENSITIVE_EXPIRED` - Sensitive data leak but expired
8. `LEAK_SENSITIVE_VALID` - Sensitive data leak with valid data (IsSensitive=true)

#### **NEW Patterns Added (5 patterns)**
9. `LEAK_MALWARE_VALID` - Tests **IsMalware=true** flag
   - Simulates malware botnet credential harvesting
   - 25M affected accounts
   
10. `LEAK_FABRICATED_VALID` - Tests **IsFabricated=true** flag
    - Simulates artificially created/fake breach data
    - 5M fake accounts
    
11. `LEAK_RETIRED_VALID` - Tests **IsRetired=true** flag
    - Simulates retired breach (no longer actively monitored)
    - 8M historical accounts
    
12. `LEAK_SUBSCRIPTION_FREE_VALID` - Tests **IsSubscriptionFree=true** flag
    - Simulates breach data available without subscription
    - 12M free-access accounts
    
13. `LEAK_MIXED_FLAGS_VALID` - Tests **multiple flags combination**
    - IsSensitive=true + IsSpamList=true + IsMalware=true
    - Complex breach scenario with multiple risk factors
    - 50M accounts with mixed risk factors

### Configuration Types (8 configurations)
1. `noRegularNoNoti` - No regular checks, no notifications
2. `pastDate` - Regular checks with past date
3. `todayDate` - Regular checks scheduled for today
4. `futureDate` - Regular checks scheduled for future
5. `thresholdDate` - Regular checks at threshold date
6. `regularInterval1` - Regular checks every 1 month
7. `regularInterval6` - Regular checks every 6 months
8. `noRegularWithInterval` - No regular checks but with interval set

## Key HIBP Flags Tested

### Security Risk Flags
- `IsVerified`: true/false - Breach verification status
- `IsFabricated`: true/false - Artificially created data
- `IsSensitive`: true/false - Contains sensitive personal data
- `IsRetired`: true/false - No longer actively monitored
- `IsSpamList`: true/false - Used for spam/credential stuffing
- `IsMalware`: true/false - Harvested by malware
- `IsSubscriptionFree`: true/false - Available without subscription

### Data Classes Covered
- Email addresses
- Passwords
- Names
- Phone numbers
- Dates of birth
- Usernames
- IP addresses
- Physical addresses
- Sexual orientations
- Ethnicities
- Genders

## Usage
```bash
cd backend
node commands/test_password_risk.js
```

This will generate 104 unique test cases covering all password risk patterns and configuration combinations, providing comprehensive test data for the password risk assessment system.

## Important Test Cases Added
The 5 new patterns specifically test edge cases and important security flags that were missing from the original implementation:

1. **Malware-based breaches** - Critical for detecting credential harvesting
2. **Fabricated data** - Important for filtering fake/artificial breaches  
3. **Retired breaches** - Handles historical data that's no longer monitored
4. **Subscription-free data** - Tests accessibility levels
5. **Complex multi-flag scenarios** - Real-world breaches often have multiple risk factors

These additions ensure comprehensive coverage of all HIBP breach classification flags and provide realistic test scenarios for the password risk assessment system.
