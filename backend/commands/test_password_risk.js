import crypto from 'crypto';
import { COLLECTION_HIBP, COLLECTION_REGULARLY_PASSWORD_CONFIGURATION } from '../constants/collections.js';
import { TTL_MILLISECOND, THIRTY_DAYS_MILLISECOND } from '../constants/constants.js';
import { saveDoc } from '../providers/firestore.js';
import { encrypt, hashEmail } from '../services/cryptography.js';

const createUniqueEmail = (keyName) => {
  const uuid = crypto.randomUUID();
  return `example-${keyName}-${uuid}@gmo.jp`;
};

const DUMMY = {
  NO_LEAK: {
    createdAt: '2025-01-30T11:40:02.133Z',
    expiredAt: '2025-02-13T11:40:01.722Z',
    result: [],
  },
  LEAK: {
    createdAt: '2025-01-30T11:41:10.017Z',
    expiredAt: '2025-02-13T11:41:09.701Z',
    result: [
      {
        Name: 'Peatix',
        Title: 'Peatix',
        Domain: 'peatix.com',
        BreachDate: '2019-01-20',
        AddedDate: '2020-12-06T22:53:53Z',
        ModifiedDate: '2020-12-06T22:53:53Z',
        PwnCount: 4227907,
        Description:
          'In January 2019, the event organising platform <a href="https://www.zdnet.com/article/hacker-leaks-the-user-data-of-event-management-app-peatix/" target="_blank" rel="noopener">Peatix suffered a data breach</a>. The incident exposed 4.2M email addresses, names and salted password hashes. The data was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Peatix.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_2: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2025-02-14T05:25:33.090Z',
    result: [
      {
        Name: 'Nihonomaru',
        Title: 'Nihonomaru',
        Domain: 'nihonomaru.net',
        BreachDate: '2015-12-01',
        AddedDate: '2016-08-30T09:54:55Z',
        ModifiedDate: '2016-08-30T09:54:55Z',
        PwnCount: 1697282,
        Description:
          'In late 2015, the anime community known as Nihonomaru had their vBulletin forum hacked and 1.7 million accounts exposed. The compromised data included email and IP addresses, usernames and salted hashes of passwords.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Nihonomaru.png',
        DataClasses: [
          'Email addresses',
          'IP addresses',
          'Passwords',
          'Usernames',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Dropbox',
        Title: 'Dropbox',
        Domain: 'dropbox.com',
        BreachDate: '2012-07-01',
        AddedDate: '2016-08-31T00:19:19Z',
        ModifiedDate: '2016-08-31T00:19:19Z',
        PwnCount: ********,
        Description:
          'In mid-2012, Dropbox suffered a data breach which exposed the stored credentials of tens of millions of their customers. In August 2016, <a href="https://motherboard.vice.com/read/dropbox-forces-password-resets-after-user-credentials-exposed" target="_blank" rel="noopener">they forced password resets for customers they believed may be at risk</a>. A large volume of data totalling over 68 million records <a href="https://motherboard.vice.com/read/hackers-stole-over-60-million-dropbox-accounts" target="_blank" rel="noopener">was subsequently traded online</a> and included email addresses and salted hashes of passwords (half of them SHA1, half of them bcrypt).',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Dropbox.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'AntiPublic',
        Title: 'Anti Public Combo List',
        Domain: '',
        BreachDate: '2016-12-16',
        AddedDate: '2017-05-04T22:07:38Z',
        ModifiedDate: '2017-05-04T22:07:38Z',
        PwnCount: *********,
        Description:
          'In December 2016, a huge list of email address and password pairs appeared in a &quot;combo list&quot; referred to as &quot;Anti Public&quot;. The list contained 458 million unique email addresses, many with multiple different passwords hacked from various online systems. The list was broadly circulated and used for &quot;credential stuffing&quot;, that is attackers employ it in an attempt to identify other online systems where the account owner had reused their password. For detailed background on this incident, read <a href="https://www.troyhunt.com/password-reuse-credential-stuffing-and-another-1-billion-records-in-have-i-been-pwned" target="_blank" rel="noopener">Password reuse, credential stuffing and another billion records in Have I Been Pwned</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Bitly',
        Title: 'Bitly',
        Domain: 'bitly.com',
        BreachDate: '2014-05-08',
        AddedDate: '2017-10-06T06:31:50Z',
        ModifiedDate: '2017-10-06T08:05:10Z',
        PwnCount: 9313136,
        Description:
          'In May 2014, the link management company <a href="https://bitly.com/blog/urgent-security-update-regarding-your-bitly-account/" target="_blank" rel="noopener">Bitly announced they\'d suffered a data breach</a>. The breach contained over 9.3 million unique email addresses, usernames and hashed passwords, most using SHA1 with a small number using bcrypt.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Bitly.png',
        DataClasses: ['Email addresses', 'Passwords', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: '2844Breaches',
        Title: '2,844 Separate Data Breaches',
        Domain: '',
        BreachDate: '2018-02-19',
        AddedDate: '2018-02-26T10:06:02Z',
        ModifiedDate: '2018-02-26T10:06:02Z',
        PwnCount: ********,
        Description:
          'In February 2018, <a href="https://www.troyhunt.com/ive-just-added-2844-new-data-breaches-with-80m-records-to-have-i-been-pwned/" target="_blank" rel="noopener">a massive collection of almost 3,000 alleged data breaches was found online</a>. Whilst some of the data had previously been seen in Have I Been Pwned, 2,844 of the files consisting of more than 80 million unique email addresses had not previously been seen. Each file contained both an email address and plain text password and were consequently loaded as a single &quot;unverified&quot; data breach.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Apollo',
        Title: 'Apollo',
        Domain: 'apollo.io',
        BreachDate: '2018-07-23',
        AddedDate: '2018-10-05T19:14:11Z',
        ModifiedDate: '2018-10-23T04:01:48Z',
        PwnCount: 125929660,
        Description:
          'In July 2018, the sales engagement startup <a href="https://www.wired.com/story/apollo-breach-linkedin-salesforce-data/" target="_blank" rel="noopener">Apollo left a database containing billions of data points publicly exposed without a password</a>. The data was discovered by security researcher <a href="http://www.vinnytroia.com/" target="_blank" rel="noopener">Vinny Troia</a> who subsequently sent a subset of the data containing 126 million unique email addresses to Have I Been Pwned. The data left exposed by Apollo was used in their &quot;revenue acceleration platform&quot; and included personal information such as names and email addresses as well as professional information including places of employment, the roles people hold and where they\'re located. Apollo stressed that the exposed data did not include sensitive information such as passwords, social security numbers or financial data. <a href="https://www.apollo.io/contact" target="_blank" rel="noopener">The Apollo website has a contact form</a> for those looking to get in touch with the organisation.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Apollo.png',
        DataClasses: [
          'Email addresses',
          'Employers',
          'Geographic locations',
          'Job titles',
          'Names',
          'Phone numbers',
          'Salutations',
          'Social media profiles',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: '',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:50:21Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists (combinations of email addresses and passwords used to hijack accounts on other services) was discovered being distributed on a popular hacking forum. The data contained almost 2.7 <em>billion</em> records including 773 million unique email addresses alongside passwords those addresses had used on other breached services. Full details on the incident and how to search the breached passwords are provided in the blog post <a href="https://www.troyhunt.com/the-773-million-record-collection-1-data-reach" target="_blank" rel="noopener">The 773 Million Record "Collection #1" Data Breach</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'AnimeGame',
        Title: 'AnimeGame',
        Domain: 'animegame.me',
        BreachDate: '2020-02-27',
        AddedDate: '2020-03-09T05:52:08Z',
        ModifiedDate: '2020-03-09T05:52:08Z',
        PwnCount: 1431378,
        Description:
          'In February 2020, the gaming website <a href="http://animegame.me/" target="_blank" rel="noopener">AnimeGame</a> suffered a data breach. The incident affected 1.4M subscribers and exposed email addresses, usernames and passwords stored as salted MD5 hashes. The data was subsequently shared on a popular hacking forum and was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/AnimeGame.png',
        DataClasses: ['Email addresses', 'Passwords', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'LeadHunter',
        Title: 'Lead Hunter',
        Domain: '',
        BreachDate: '2020-03-04',
        AddedDate: '2020-06-03T10:55:31Z',
        ModifiedDate: '2020-06-03T10:55:31Z',
        PwnCount: 68693853,
        Description:
          'In March 2020, <a href="https://www.troyhunt.com/the-unattributable-lead-hunter-data-breach" target="_blank" rel="noopener">a massive trove of personal information referred to as &quot;Lead Hunter&quot;</a> was provided to HIBP after being found left exposed on a publicly facing Elasticsearch server. The data contained 69 million unique email addresses across 110 million rows of data accompanied by additional personal information including names, phone numbers, genders and physical addresses. At the time of publishing, the breach could not be attributed to those responsible for obtaining and exposing it. The data was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: [
          'Email addresses',
          'Genders',
          'IP addresses',
          'Names',
          'Phone numbers',
          'Physical addresses',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Cit0day',
        Title: 'Cit0day',
        Domain: 'cit0day.in',
        BreachDate: '2020-11-04',
        AddedDate: '2020-11-19T08:07:33Z',
        ModifiedDate: '2020-11-19T08:07:33Z',
        PwnCount: 226883414,
        Description:
          'In November 2020, <a href="https://www.troyhunt.com/inside-the-cit0day-breach-collection" target="_blank" rel="noopener">a collection of more than 23,000 allegedly breached websites known as Cit0day were made available for download on several hacking forums</a>. The data consisted of 226M unique email address alongside password pairs, often represented as both password hashes and the cracked, plain text versions. Independent verification of the data established it contains many legitimate, previously undisclosed breaches. The data was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Peatix',
        Title: 'Peatix',
        Domain: 'peatix.com',
        BreachDate: '2019-01-20',
        AddedDate: '2020-12-06T22:53:53Z',
        ModifiedDate: '2020-12-06T22:53:53Z',
        PwnCount: 4227907,
        Description:
          'In January 2019, the event organising platform <a href="https://www.zdnet.com/article/hacker-leaks-the-user-data-of-event-management-app-peatix/" target="_blank" rel="noopener">Peatix suffered a data breach</a>. The incident exposed 4.2M email addresses, names and salted password hashes. The data was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Peatix.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Nitro',
        Title: 'Nitro',
        Domain: 'gonitro.com',
        BreachDate: '2020-09-28',
        AddedDate: '2021-01-19T10:45:32Z',
        ModifiedDate: '2021-01-19T10:45:32Z',
        PwnCount: 77159696,
        Description:
          'In September 2020, <a href="https://www.bleepingcomputer.com/news/security/massive-nitro-data-breach-impacts-microsoft-google-apple-more/" target="_blank" rel="noopener">the Nitro PDF service suffered a massive data breach which exposed over 70 million unique email addresses</a>. The breach also exposed names, bcrypt password hashes and the titles of converted documents. The data was provided to HIBP by <a href="https://dehashed.com/" target="_blank" rel="noopener">dehashed.com</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Nitro.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Epik',
        Title: 'Epik',
        Domain: 'epik.com',
        BreachDate: '2021-09-13',
        AddedDate: '2021-09-19T21:27:17Z',
        ModifiedDate: '2021-09-19T21:27:17Z',
        PwnCount: 15003961,
        Description:
          'In September 2021, <a href="https://arstechnica.com/information-technology/2021/09/anonymous-leaks-gigabytes-of-data-from-epik-web-host-of-gab-and-parler/" target="_blank" rel="noopener">the domain registrar and web host Epik suffered a significant data breach</a>, allegedly in retaliation for hosting alt-right websites. The breach exposed a huge volume of data not just of Epik customers, but also scraped WHOIS records belonging to individuals and organisations who were not Epik customers. The data included over 15 million unique email addresses (including anonymised versions for domain privacy), names, phone numbers, physical addresses, purchases and passwords stored in various formats.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Epik.png',
        DataClasses: [
          'Email addresses',
          'Names',
          'Phone numbers',
          'Physical addresses',
          'Purchases',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Gravatar',
        Title: 'Gravatar',
        Domain: 'gravatar.com',
        BreachDate: '2020-10-03',
        AddedDate: '2021-12-05T22:45:58Z',
        ModifiedDate: '2021-12-08T01:47:02Z',
        PwnCount: 113990759,
        Description:
          'In October 2020, <a href="https://www.bleepingcomputer.com/news/security/online-avatar-service-gravatar-allows-mass-collection-of-user-info/" target="_blank" rel="noopener">a security researcher published a technique for scraping large volumes of data from Gravatar, the service for providing globally unique avatars </a>. 167 million names, usernames and MD5 hashes of email addresses used to reference users\' avatars were subsequently scraped and distributed within the hacking community. 114 million of the MD5 hashes were cracked and distributed alongside the source hash, thus disclosing the original email address and accompanying data. Following the impacted email addresses being searchable in HIBP, <a href="https://en.gravatar.com/support/data-privacy" target="_blank" rel="noopener">Gravatar release an FAQ detailing the incident</a>.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Gravatar.png',
        DataClasses: ['Email addresses', 'Names', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Twitter200M',
        Title: 'Twitter (200M)',
        Domain: 'twitter.com',
        BreachDate: '2021-01-01',
        AddedDate: '2023-01-05T20:49:16Z',
        ModifiedDate: '2023-01-05T20:49:16Z',
        PwnCount: *********,
        Description:
          'In early 2023, <a href="https://www.bleepingcomputer.com/news/security/200-million-twitter-users-email-addresses-allegedly-leaked-online/" target="_blank" rel="noopener">over 200M records scraped from Twitter appeared on a popular hacking forum</a>. The data was obtained sometime in 2021 by abusing an API that enabled email addresses to be resolved to Twitter profiles. The subsequent results were then composed into a corpus of data containing email addresses alongside public Twitter profile information including names, usernames and follower counts.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Twitter.png',
        DataClasses: [
          'Email addresses',
          'Names',
          'Social media profiles',
          'Usernames',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'JoyGames',
        Title: 'JoyGames',
        Domain: 'joygames.me',
        BreachDate: '2019-12-14',
        AddedDate: '2023-12-07T05:52:24Z',
        ModifiedDate: '2023-12-07T05:52:24Z',
        PwnCount: 4461787,
        Description:
          'In December 2019, the forum for the JoyGames website suffered a data breach that exposed 4.5M unique email addresses. The impacted data also included usernames, IP addresses and salted MD5 password hashes.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/JoyGames.png',
        DataClasses: [
          'Email addresses',
          'IP addresses',
          'Passwords',
          'Usernames',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Gemplex',
        Title: 'Gemplex',
        Domain: 'gemplex.tv',
        BreachDate: '2021-02-18',
        AddedDate: '2023-12-09T02:19:40Z',
        ModifiedDate: '2023-12-09T02:19:40Z',
        PwnCount: 4563166,
        Description:
          'In February 2021, the Indian streaming platform Gemplex suffered a data breach that exposed 4.6M user accounts. The impacted data included device information, names, phone numbers, email addresses and bcrypt password hashes.',
        LogoPath:
          'https://haveibeenpwned.com/Content/Images/PwnedLogos/Gemplex.png',
        DataClasses: [
          'Device information',
          'Email addresses',
          'Names',
          'Passwords',
          'Phone numbers',
        ],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_MULTIPLE_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'Nihonomaru',
        Title: 'Nihonomaru',
        Domain: 'nihonomaru.com',
        BreachDate: '2020-01-01',
        AddedDate: '2020-12-06T22:53:53Z',
        ModifiedDate: '2020-12-06T22:53:53Z',
        PwnCount: 1000000,
        Description:
          'In 2020, the Japanese website Nihonomaru suffered a data breach. The incident exposed email addresses, names and passwords.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Default.png',
        DataClasses: ['Email addresses', 'Names', 'Passwords'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
      {
        Name: 'Adobe',
        Title: 'Adobe',
        Domain: 'adobe.com',
        BreachDate: '2013-10-04',
        AddedDate: '2013-12-04T00:00:00Z',
        ModifiedDate: '2013-12-04T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2013, 153 million Adobe accounts were breached with each containing an internal ID, username, email, encrypted password and a password hint in plain text.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/Adobe.png',
        DataClasses: ['Email addresses', 'Password hints', 'Passwords', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_HIGH_SEVERITY_EXPIRED: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: 'collection1.com',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:46:07Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists (combinations of email addresses and passwords used to hijack accounts on other services) was discovered being distributed on a popular hacking forum.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: true,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_HIGH_SEVERITY_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'Collection1',
        Title: 'Collection #1',
        Domain: 'collection1.com',
        BreachDate: '2019-01-07',
        AddedDate: '2019-01-16T21:46:07Z',
        ModifiedDate: '2019-01-16T21:46:07Z',
        PwnCount: *********,
        Description:
          'In January 2019, a large collection of credential stuffing lists (combinations of email addresses and passwords used to hijack accounts on other services) was discovered being distributed on a popular hacking forum.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/List.png',
        DataClasses: ['Email addresses', 'Passwords'],
        IsVerified: false,
        IsFabricated: false,
        IsSensitive: false,
        IsRetired: false,
        IsSpamList: true,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_SENSITIVE_EXPIRED: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2024-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'AdultFriendFinder',
        Title: 'Adult FriendFinder',
        Domain: 'adultfriendfinder.com',
        BreachDate: '2016-10-01',
        AddedDate: '2016-11-14T00:00:00Z',
        ModifiedDate: '2016-11-14T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
        DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: true,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
  LEAK_SENSITIVE_VALID: {
    createdAt: '2025-01-31T05:25:33.735Z',
    expiredAt: '2999-12-31T23:59:59.000Z',
    result: [
      {
        Name: 'AdultFriendFinder',
        Title: 'Adult FriendFinder',
        Domain: 'adultfriendfinder.com',
        BreachDate: '2016-10-01',
        AddedDate: '2016-11-14T00:00:00Z',
        ModifiedDate: '2016-11-14T00:00:00Z',
        PwnCount: *********,
        Description:
          'In October 2016, the adult website Adult FriendFinder was hacked and over 400 million accounts were exposed.',
        LogoPath: 'https://haveibeenpwned.com/Content/Images/PwnedLogos/AdultFriendFinder.png',
        DataClasses: ['Dates of birth', 'Email addresses', 'Ethnicities', 'Genders', 'Names', 'Passwords', 'Phone numbers', 'Sexual orientations', 'Usernames'],
        IsVerified: true,
        IsFabricated: false,
        IsSensitive: true,
        IsRetired: false,
        IsSpamList: false,
        IsMalware: false,
        IsSubscriptionFree: false,
      },
    ],
  },
};

const configurations = {
  noRegularNoNoti: {
    isRegularly: false,
    interval: 1,
    isNotification: false,
    nextCheckedAt: null,
  },
  pastDate: {
    isRegularly: true,
    interval: 3,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10') - THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  todayDate: {
    isRegularly: true,
    interval: 3,
    isNotification: false,
    nextCheckedAt: new Date().toISOString(),
  },
  futureDate: {
    isRegularly: false,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  thresholdDate: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(Date.parse('2025-03-10')).toISOString(),
  },
  regularInterval1: {
    isRegularly: true,
    interval: 1,
    isNotification: true,
    nextCheckedAt: new Date(new Date().getTime() + THIRTY_DAYS_MILLISECOND).toISOString(),
  },
  regularInterval6: {
    isRegularly: true,
    interval: 6,
    isNotification: false,
    nextCheckedAt: new Date(new Date().getTime() + (6 * THIRTY_DAYS_MILLISECOND)).toISOString(),
  },
  noRegularWithInterval: {
    isRegularly: false,
    interval: 3,
    isNotification: true,
    nextCheckedAt: null,
  },
};

const run = async () => {
  for (const configKey in configurations) {
    const configuration = configurations[configKey];

    for (const keyName in DUMMY) {
      const dataHibp = { ...DUMMY[keyName] };
      const uniqueEmail = createUniqueEmail(keyName);
      dataHibp.email = uniqueEmail;

      const now = new Date();
      const isExpired = keyName.includes('EXPIRED');
      const expiredAt = isExpired ? new Date('2024-12-31T23:59:59.000Z') : new Date(now.getTime() + TTL_MILLISECOND);
      const hashedEmail = await hashEmail(uniqueEmail);

      const code = await encrypt(
        JSON.stringify({ email: uniqueEmail, expiredAt: expiredAt.getTime() }),
        process.env.SECRET_CRYPTOGRAPHY_PASSWORD,
        process.env.SECRET_CRYPTOGRAPHY_SALT,
      );

      const names = dataHibp.result
        .filter(r => r.DataClasses.includes('Passwords'))
        .map(r => r.Name);

      const count = names.length;

      await saveDoc({
        collection: COLLECTION_HIBP,
        docId: code,
        data: {
          ...dataHibp,
          configuration,
        },
      });

      await saveDoc({
        collection: COLLECTION_REGULARLY_PASSWORD_CONFIGURATION,
        docId: hashedEmail,
        data: {
          encryptedEmail: uniqueEmail,
          isRegularly: configuration.isRegularly,
          interval: configuration.interval,
          nextCheckedAt: configuration.nextCheckedAt,
          isNotification: configuration.isNotification,
          history: [{
            createdAt: dataHibp.createdAt,
            code: code,
            names: names,
            count: count,
          }],
        },
      });

      console.log({
        email: uniqueEmail,
        createdAt: dataHibp.createdAt,
        expiredAt: dataHibp.expiredAt,
        type: keyName,
        configType: configKey,
        configuration,
        expired: isExpired,
        leakedSites: names.join(', ') || 'None',
        leakCount: count,
        url: `${process.env.HOST}${process.env.PATH_PREFIX}/check/password/?code=${code}`,
      });
    }
  }
};
run();
