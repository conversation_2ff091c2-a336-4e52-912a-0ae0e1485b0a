# Site Risk Test Patterns Summary

## Overview
This document summarizes the comprehensive site risk test patterns implemented in `test_site_risk.js` after maintenance and enhancement.

## Current Implementation Status

### SSL Patterns (14 patterns)
1. `NOT_FREE_1_EXPIRED/VALID` - Standard paid SSL with hostname/cert verification: true/true
2. `NOT_FREE_2_EXPIRED/VALID` - Paid SSL with hostname verification: false, cert verification: true  
3. `NOT_FREE_3_EXPIRED/VALID` - Paid SSL with hostname verification: true, cert verification: false
4. `FREE_1_EXPIRED` - Let's Encrypt SSL (expired)
5. `FREE_2` - Let's Encrypt SSL (15 days remaining)
6. `FREE_3` - Let's Encrypt SSL (expired)
7. `ERROR_1` - SS<PERSON> with hostname verification error
8. `ERROR_2` - SSL with certificate verification error
9. **NEW** `MIXED_CERT_VALID_EXPIRED` - Mixed valid and expired certificates
10. **NEW** `BOTH_VERIFICATION_FAILED` - Both hostname and certificate verification failed

### Cloud Patterns (10 patterns)
1. `AWS_EXPIRED/VALID` - Amazon Web Services cloud detection
2. `GCP_EXPIRED/VALID` - Google Cloud Platform detection  
3. `AZURE_EXPIRED/VALID` - Microsoft Azure cloud detection
4. `NO_CLOUD_EXPIRED/VALID` - No cloud provider detected
5. `CLOUD_ERROR_EXPIRED/VALID` - Cloud detection error states

### NDS (Network Diagnostic Scan) Ranks (5 ranks)
- **A**: Safe (1 vulnerability - info level)
- **B**: Safe (2 vulnerabilities - info level)  
- **C**: Requires action (3 vulnerabilities - info/low level)
- **D**: Requires action (4 vulnerabilities - info/low/medium level)
- **E**: Urgent response required (7 vulnerabilities - info/low/medium/high/critical level)

### Impersonation Ranks (4 ranks)
- **A**: Safe
- **B**: Caution required
- **C**: Dangerous  
- **D**: Very dangerous

### **NEW** Breach Data Patterns (4 patterns + None)
1. **None** - No breach data (original behavior)
2. **EXPIRED_BREACHES** - Sites with expired/retired breaches from 2019
3. **UNVERIFIED_BREACHES** - Sites with unverified breach data requiring investigation
4. **MIXED_BREACH_TYPES** - Sites with both password and non-password breaches
5. **HISTORICAL_MIXED_SENSITIVITY** - Sites with extremely old (2015) and recent sensitive data breaches

### SPF Configurations (3 options)
- `undefined` - No SPF record
- `true` - Valid SPF record
- `false` - Invalid SPF record

### DMARC Configurations (5 options)  
- `undefined` - No DMARC record
- `null` - DMARC record exists but null policy
- `{policy: 'none'}` - DMARC with no enforcement
- `{policy: 'quarantine'}` - DMARC with quarantine policy
- `{policy: 'reject'}` - DMARC with reject policy

## Test Data Generation Statistics

### Total Combinations
- **Base Patterns**: 14 SSL × 10 Cloud × 5 NDS Ranks × 5 Breach Patterns = **3,500 combinations**
- **Additional D/E Rank Combinations**: 14 SSL × 10 Cloud × 2 Ranks × 5 Breach Patterns = **1,400 combinations**
- **Grand Total**: **4,900 unique test cases**

### Pattern Coverage
Each test case includes:
- SSL certificate status and verification states
- Cloud provider detection results  
- Network vulnerability scan results with appropriate risk levels
- Domain impersonation risk assessment
- Email authentication (SPF/DMARC) configurations
- **NEW** Breach data scenarios covering various security incident types
- Regular monitoring configuration (random intervals: 1, 3, or 6 months)

## Key Improvements Made

### 1. Added Missing Breach Patterns
- **Expired Breaches**: Historical breaches that are no longer relevant
- **Unverified Breaches**: Breach data requiring verification
- **Mixed Breach Types**: Combination of password and non-password data breaches  
- **Historical Mixed Sensitivity**: Very old breaches combined with recent sensitive data

### 2. Enhanced SSL Coverage
- Added mixed certificate scenarios (valid/expired combinations)
- Added comprehensive verification failure patterns
- Better coverage of edge cases

### 3. Improved Test Data Quality
- More realistic breach data with proper HIBP-style flags
- Comprehensive data class coverage (emails, passwords, names, phone numbers, etc.)
- Proper sensitivity and verification flags
- Realistic breach dates and affected user counts

### 4. Better Logging and Monitoring
- Detailed generation statistics
- Pattern breakdown reporting
- Progress tracking during generation
- Clear summary of what was generated

## Usage

```bash
cd backend
node commands/test_site_risk.js
```

This will generate comprehensive test data covering all site risk evaluation scenarios, providing thorough coverage for testing the site risk assessment system across all supported patterns and edge cases.

## Breach Data Flags Tested

### Security Risk Indicators
- `isVerified`: true/false - Breach verification status
- `isFabricated`: false - Not artificially created  
- `isSensitive`: true/false - Contains sensitive personal data
- `isRetired`: true/false - No longer actively monitored
- `isSpamList`: false - Not used for spam/credential stuffing
- `isMalware`: false - Not harvested by malware
- `isSubscriptionFree`: true/false - Availability without subscription

### Data Classes Covered
- Email addresses, Passwords, Usernames
- Names, Phone numbers, Physical addresses  
- Dates of birth, Sexual orientations, Ethnicities
- And other sensitive personal information categories
